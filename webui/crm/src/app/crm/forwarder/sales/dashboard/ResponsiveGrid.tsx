import React, { ReactNode } from "react";
import { bs, } from '@datatp-ui/lib';

const DEFAULTS = {
  CELL_HEIGHT: 20,
  CELL_WIDTH: 120,
  HEADER_CSS: 'flex-hbox-grow-0 border-bottom bg-body-highlight py-1 fw-bold',
  CELL_CSS: 'flex-hbox-grow-0 align-items-center mx-1 text-body fs-9',
  ROW_CSS: 'flex-hbox-grow-0',
  CONTAINER_CSS: 'flex-vbox'
};

export interface GridColumn {
  label: string;
  field: string;
  cssClass?: string;
  width?: number;
  headerRenderer?: (column: GridColumn) => ReactNode;
  cellRenderer?: (value: any, record: any, column: GridColumn) => ReactNode;
}

export interface GridConfig {
  header?: {
    height?: number;
    cssClass?: string;
    renderer?: (columns: Array<GridColumn>, config: GridConfig) => ReactNode;
  },
  row?: {
    height?: number;
    cssClass?: string;
    renderer?: (record: any, index: number, columns: Array<GridColumn>, config: GridConfig) => ReactNode;
  },
  columns: Array<GridColumn>;
  showHeader?: boolean;
  showBorder?: boolean;
  containerCssClass?: string;

  widthConfig?: {
    totalWidth?: number;      // Total width for all columns
    unit?: number;            // Base unit for width calculations
    ratios?: Array<number>;   // Column width ratios (alternative to setting on each column)
    minColumnWidth?: number;  // Minimum width for any column
  };
}

export interface ResponsiveGridProps extends bs.ELEProps {
  config: GridConfig;
  data: Array<any>;
  onRowClick?: (record: any, index: number) => void;
  onCellClick?: (record: any, column: GridColumn, value: any) => void;
}

export class ResponsiveGrid extends bs.AvailableSize<ResponsiveGridProps> {

  calculateColumnWidths(availableWidth: number): void {
    const { config } = this.props;
    const { columns, widthConfig } = config;

    if (!widthConfig) return;

    const totalWidth = widthConfig.totalWidth || availableWidth;
    const minColumnWidth = widthConfig.minColumnWidth || 80;

    if (!widthConfig.ratios || widthConfig.ratios.length !== columns.length) {
      // If no ratios specified, use equal distribution
      const equalRatio = 1;
      const totalRatio = columns.length * equalRatio;
      const unit = totalWidth / totalRatio;

      columns.forEach(column => {
        column.width = Math.max(Math.floor(unit), minColumnWidth);
      });
      return;
    }

    const totalRatio = widthConfig.ratios.reduce((sum, ratio) => sum + ratio, 0);
    const unit = widthConfig.unit || (totalWidth / totalRatio);

    columns.forEach((column, index) => {
      const calculatedWidth = Math.floor(unit * widthConfig.ratios![index]);
      column.width = Math.max(calculatedWidth, minColumnWidth);
    });
  }

  renderHeaderCell(column: GridColumn, headerHeight: number): ReactNode {
    const { config } = this.props;

    if (column.headerRenderer) {
      return column.headerRenderer(column);
    }

    const width = column.width || DEFAULTS.CELL_WIDTH;
    const style = { width, height: headerHeight };
    const cellCssClass = bs.mergeCssClass(DEFAULTS.CELL_CSS, column.cssClass);

    return (
      <div key={column.field} className={cellCssClass} style={style}>
        {column.label}
      </div>
    );
  }

  renderHeader(): ReactNode {
    const { config } = this.props;
    if (!config.showHeader) return null;
    if (config.header?.renderer) {
      return config.header.renderer(config.columns, config);
    }
    const headerHeight = config.header?.height || DEFAULTS.CELL_HEIGHT;
    const headerCells = config.columns.map(column => this.renderHeaderCell(column, headerHeight));

    let rowCssClass = config.header?.cssClass || DEFAULTS.HEADER_CSS;
    if (config.showBorder) rowCssClass = bs.mergeCssClass(rowCssClass, 'border-bottom');

    return (
      <div className={rowCssClass}>
        {headerCells}
      </div>
    );
  }

  renderCell(record: any, column: GridColumn, cellHeight: number): ReactNode {
    const value = record[column.field];
    if (column.cellRenderer) {
      return column.cellRenderer(value, record, column);
    }
    const { onCellClick } = this.props;
    const width = column.width || DEFAULTS.CELL_WIDTH;
    const style = { width, height: cellHeight };
    const cellCssClass = bs.mergeCssClass(DEFAULTS.CELL_CSS, column.cssClass);

    return (
      <div key={column.field} className={cellCssClass} style={style}
        onClick={onCellClick ? () => onCellClick(record, column, value) : undefined} >
        {value}
      </div>
    );
  }

  renderRow(record: any, index: number): ReactNode {
    const { config, onRowClick } = this.props;
    const cellHeight = config.row?.height || DEFAULTS.CELL_HEIGHT;

    // Use custom row renderer if provided
    if (config.row?.renderer) {
      return config.row.renderer(record, index, config.columns, config);
    }

    const cells = config.columns.map(column =>
      this.renderCell(record, column, cellHeight)
    );

    let rowCssClass = config.row?.cssClass || DEFAULTS.ROW_CSS;
    if (config.showBorder) rowCssClass = bs.mergeCssClass(rowCssClass, 'border-bottom');

    return (
      <div key={`row-${index}`} className={rowCssClass}
        onClick={onRowClick ? () => onRowClick(record, index) : undefined} >
        {cells}
      </div>
    );
  }

  getTotalColumnsWidth(): number {
    const { config } = this.props;
    return config.columns.reduce((sum, column) => sum + (column.width || DEFAULTS.CELL_WIDTH), 0);
  }

  renderContent(width: number | string, _height: number): React.ReactElement {
    const { className, style, config, data } = this.props;
    const containerClass = className || config.containerCssClass || DEFAULTS.CONTAINER_CSS;
    const contentWidth = typeof width === 'number' ? width : parseInt(width as string, 10) || 800;
    const padding = 10; // Padding for container
    const availableWidth = contentWidth - (padding * 2);

    if (config.widthConfig) {
      this.calculateColumnWidths(availableWidth);
    }

    const totalColumnsWidth = this.getTotalColumnsWidth();
    const needsScroll = totalColumnsWidth > availableWidth;

    const rows = data.map((record, index) =>
      this.renderRow(record, index)
    );

    return (
      <div className={containerClass} style={{ ...style, width: contentWidth }}>
        <div style={{ height: 20 }}>
          {this.renderHeader()}
        </div>
        <div className="h-100" style={{ padding: padding, overflowX: needsScroll ? 'auto' : 'hidden' }} >
          <div style={{ width: needsScroll ? totalColumnsWidth : '100%' }}>
            {rows}
          </div>
        </div>
      </div>
    );
  }

}

