import React from "react";
import * as FeatherIcon from 'react-feather';
import { bs, app, util, sql } from '@datatp-ui/lib';
import { GridConfig, ResponsiveGrid } from "./ResponsiveGrid";
import { WQuickTimeRangeSelector } from "./UIDashboardUtility";

const SESSION = app.host.DATATP_HOST.session;

const salemanSystemPerformanceGridConfig: GridConfig = {
  header: {
    height: 20,
  },
  row: {
    height: 30,
  },
  showHeader: true,
  showBorder: true,
  columns: [
    { field: 'employeeLabel', label: '<PERSON>an' },
    { field: 'companyBranch', label: 'Branch' },
    { field: 'totalTasks', label: 'Total Tasks' },
    { field: 'meetCustomerTasks', label: 'Meet Customer Tasks' },
    { field: 'totalRequestsPricing', label: 'Total Requests' },
    { field: 'newCustomerCount', label: 'New Customers' },
    { field: 'newLeadCount', label: 'New Leads' },
    { field: 'noResponseRequests', label: 'No Response Req.' },
    { field: 'overdueRequestCount', label: 'Overdue Req. Count' },
    { field: 'bookingCount', label: 'Booking Count' },
  ],
  widthConfig: {
    totalWidth: 1350,
    minColumnWidth: 150,
    ratios: [2, 1, 1, 1, 1, 1, 1, 1, 1, 1]
  }
};

interface ReportFilter {
  dateFilter: { fromValue: string, toValue: string, label: string };
  company: { code: string, label: string };
}

export interface UISalemanSystemPerformanceProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System';
}
export class UISalemanSystemPerformance extends app.AppComponent<UISalemanSystemPerformanceProps> {
  reportFilter: ReportFilter;
  records: Array<any> = [];

  constructor(props: UISalemanSystemPerformanceProps) {
    super(props);

    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    let dateFilter = new util.TimeRange();
    dateFilter.fromSetDate(firstDayOfMonth);
    dateFilter.toSetDate(lastDayOfMonth);

    let companyContext = SESSION.getCurrentCompanyContext();
    let companyCode: string = companyContext['companyCode'];
    let companyLabel: string = companyContext['companyLabel'];

    this.reportFilter = {
      company: { code: companyCode, label: companyLabel },
      dateFilter: { fromValue: dateFilter.fromFormat(), toValue: dateFilter.toFormat(), label: 'This Month' },
    }
    this.loadData();
  }

  componentDidMount(): void {
    this.loadData();
  }

  loadData(): void {
    const { appContext, space } = this.props;
    const { company, dateFilter } = this.reportFilter;

    if ((dateFilter.fromValue || '').trim()) {
    }

    if ((dateFilter.toValue || '').trim()) {
    }

    let fromDate: Date = util.TimeUtil.parseCompactDateTimeFormat(dateFilter.fromValue);
    let toDate: Date = util.TimeUtil.parseCompactDateTimeFormat(dateFilter.toValue);

    let searchParams: sql.SqlSearchParams = {
      params: { companyId: 8 },
      filters: [...sql.createSearchFilter()],
      rangeFilters: [
        ...sql.createDateTimeFilter("reportDate", ("Report Date"), new util.TimeRange(fromDate, toDate)),
      ],
      maxReturn: 100000
    }

    this.markLoading(true);
    appContext.createHttpBackendCall('SaleTaskReportService', 'salemanSystemPerformanceReport', { params: searchParams })
      .withSuccessData((records: Array<any>) => {
        for (let rec of records) {
          rec['employeeLabel'] = util.text.formater.uiTruncate(rec.employeeLabel || 'N/A', 280, true);
        }
        this.records = records;
        this.markLoading(false);
        this.forceUpdate();
      })
      .call()
  }

  onModify = (bean: any, _field: string, _oldVal: any, newVal: any) => {
    // this.viewId = util.IDTracker.next();
    bean[_field] = newVal;
    this.forceUpdate();
    this.loadData();
  }

  render(): React.ReactNode {
    const { appContext, pageContext } = this.props;
    const { dateFilter } = this.reportFilter;

    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)',
      backgroundColor: lightColor
    };

    return (
      <div className="flex-vbox mx-2 p-2 bg-white rounded-md w-100 h-100 mb-1">

        <div className="d-flex flex-column flex-md-row gap-2 p-1">

          <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
            style={{
              minWidth: 900,
              borderColor: borderColor,
              transition: 'all 0.3s ease',
              marginBottom: '10px'
            }}
            onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
            onMouseLeave={(e) => {
              e.currentTarget.style.boxShadow = 'none';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.backgroundColor = '#fff';
            }}>
            <div className="flex-vbox mx-2 p-2 bg-white rounded-md w-100 h-100">
              <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">
                <h5 style={{ color: '#6c757d' }}><FeatherIcon.TrendingUp className="me-2" size={18} />Salesman Activity Tracker</h5>

                <div className="flex-hbox justify-content-end align-items-center flex-grow-1">

                  <WQuickTimeRangeSelector appContext={appContext} pageContext={pageContext}
                    initBean={dateFilter}
                    onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
                      this.reportFilter.dateFilter = bean;
                      this.loadData();
                    }} />

                  <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
                    onClick={() => { }}>
                    <FeatherIcon.Download size={14} className="me-1" />
                    Export
                  </bs.Button>

                </div>
              </div>

              <div className="flex-vbox" style={{ maxHeight: 600, minHeight: 400 }}>
                <ResponsiveGrid className="w-100 h-100" config={salemanSystemPerformanceGridConfig} data={this.records} />
              </div>
            </div>
          </div>
        </div>

      </div>
    )

  }
}
